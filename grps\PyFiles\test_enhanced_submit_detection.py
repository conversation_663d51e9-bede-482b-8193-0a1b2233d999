#!/usr/bin/env python3
"""
Test script for Enhanced Submit Button Detection
Tests the new comprehensive submit button selectors and staleness handling.
"""

import sys
import os
import logging
from datetime import datetime

# Add the parent directory to the path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_submit_button_selectors():
    """Test the comprehensive submit button selectors"""
    logger.info("Testing enhanced submit button selectors...")
    
    # The new comprehensive selectors from the enhanced method
    submit_buttons = [
        # Modern Material Design 3 selectors (highest priority)
        '//button[contains(@class, "VfPpkd-LgbsSe") and contains(@class, "VfPpkd-kJFWHd")]//span[contains(text(), "Next")]',
        '//button[contains(@class, "VfPpkd-LgbsSe") and contains(@class, "VfPpkd-kJFWHd")]//span[contains(text(), "Continue")]',
        '//button[contains(@class, "VfPpkd-LgbsSe") and contains(@class, "VfPpkd-kJFWHd")]//span[contains(text(), "Verify")]',
        '//button[contains(@class, "VfPpkd-LgbsSe") and contains(@class, "VfPpkd-kJFWHd")]//span[contains(text(), "Suivant")]',
        
        # Material Design button with data attributes
        '//button[@data-mdc-dialog-action="ok"]',
        '//button[@data-mdc-dialog-action="next"]',
        '//button[@data-mdc-dialog-action="continue"]',
        
        # Google-specific button patterns
        '//button[contains(@class, "VfPpkd-LgbsSe") and @jsname]',
        '//button[contains(@class, "VfPpkd-LgbsSe") and contains(@aria-label, "Next")]',
        '//button[contains(@class, "VfPpkd-LgbsSe") and contains(@aria-label, "Continue")]',
        
        # Modern div-based buttons with role
        '//div[@role="button" and contains(@class, "VfPpkd-LgbsSe")]//span[contains(text(), "Next")]',
        '//div[@role="button" and contains(@class, "VfPpkd-LgbsSe")]//span[contains(text(), "Continue")]',
        '//div[@role="button" and contains(@class, "VfPpkd-Bz112c")]',
        
        # Generic Material Design patterns
        '//button[contains(@class, "mdc-button") and contains(@class, "mdc-button--raised")]',
        '//button[contains(@class, "mdc-button") and contains(@class, "mdc-button--unelevated")]',
        
        # Text-based selectors (medium priority)
        '//button[contains(text(), "Next")]',
        '//button[contains(text(), "Continue")]',
        '//button[contains(text(), "Verify")]',
        '//button[contains(text(), "Submit")]',
        '//button[contains(text(), "Suivant")]',  # French
        '//button[contains(text(), "Continuer")]',  # French
        '//button[contains(text(), "Vérifier")]',  # French Verify
        
        # Span-based text with parent button
        '//span[contains(text(), "Next")]/parent::button',
        '//span[contains(text(), "Continue")]/parent::button',
        '//span[contains(text(), "Verify")]/parent::button',
        '//span[contains(text(), "Suivant")]/parent::button',
        
        # Legacy selectors (lower priority)
        '//input[@type="submit"]',
        '//button[@type="submit"]',
        '//*[@id="next"]',
        '//*[@id="continue"]',
        '//*[@id="submit"]',
        
        # Role-based selectors
        '//div[@role="button" and contains(text(), "Next")]',
        '//div[@role="button" and contains(text(), "Continue")]',
        '//div[@role="button" and contains(text(), "Suivant")]',
        
        # Fallback class-based selectors
        '//button[contains(@class, "VfPpkd-LgbsSe")]',
        '//div[contains(@class, "VfPpkd-LgbsSe") and @role="button"]',
        '//button[contains(@class, "primary")]',
        '//button[contains(@class, "submit")]'
    ]
    
    # Validate selector syntax
    valid_selectors = 0
    invalid_selectors = []
    
    for i, selector in enumerate(submit_buttons):
        try:
            # Basic XPath validation
            if selector.startswith('//') and ('[' in selector or '@' in selector):
                valid_selectors += 1
                logger.debug(f"✓ Selector {i+1}: {selector[:60]}...")
            else:
                invalid_selectors.append((i+1, selector))
                logger.warning(f"✗ Invalid selector {i+1}: {selector}")
        except Exception as e:
            invalid_selectors.append((i+1, selector))
            logger.warning(f"✗ Error validating selector {i+1}: {str(e)}")
    
    total_selectors = len(submit_buttons)
    success_rate = (valid_selectors / total_selectors) * 100
    
    logger.info(f"Submit button selector validation:")
    logger.info(f"  Total selectors: {total_selectors}")
    logger.info(f"  Valid selectors: {valid_selectors}")
    logger.info(f"  Invalid selectors: {len(invalid_selectors)}")
    logger.info(f"  Success rate: {success_rate:.1f}%")
    
    if invalid_selectors:
        logger.warning("Invalid selectors found:")
        for idx, selector in invalid_selectors:
            logger.warning(f"  {idx}: {selector}")
    
    return success_rate >= 95.0

def test_sms_input_selectors():
    """Test SMS input element selectors for staleness handling"""
    logger.info("Testing SMS input element selectors...")
    
    sms_selectors = [
        '//input[@id="idvAnyPhonePin"]',
        '//input[@name="pin"]',
        '//input[@type="tel" and contains(@aria-label, "Enter code")]',
        '//input[@type="tel" and @pattern="[0-9 ]*"]',
        '//input[contains(@aria-label, "verification code")]',
        '//input[contains(@placeholder, "Enter code")]',
        '//input[@type="tel"]',
        '//input[@type="text" and contains(@placeholder, "code")]'
    ]
    
    valid_selectors = 0
    for i, selector in enumerate(sms_selectors):
        try:
            if selector.startswith('//input') and '@' in selector:
                valid_selectors += 1
                logger.debug(f"✓ SMS selector {i+1}: {selector}")
            else:
                logger.warning(f"✗ Invalid SMS selector {i+1}: {selector}")
        except Exception as e:
            logger.warning(f"✗ Error validating SMS selector {i+1}: {str(e)}")
    
    total_selectors = len(sms_selectors)
    success_rate = (valid_selectors / total_selectors) * 100
    
    logger.info(f"SMS input selector validation:")
    logger.info(f"  Total selectors: {total_selectors}")
    logger.info(f"  Valid selectors: {valid_selectors}")
    logger.info(f"  Success rate: {success_rate:.1f}%")
    
    return success_rate >= 90.0

def test_verification_completion_indicators():
    """Test verification completion detection indicators"""
    logger.info("Testing verification completion indicators...")
    
    completion_indicators = [
        # URL changes indicating success
        'myaccount.google.com',
        'accounts.google.com/signin/v2/challenge/selection',
        'mail.google.com',
        'gmail.com',
        
        # Page elements indicating success or next step
        '//div[contains(text(), "verified")]',
        '//div[contains(text(), "success")]',
        '//h1[contains(text(), "Welcome")]',
        '//div[contains(@class, "success")]'
    ]
    
    url_indicators = completion_indicators[:4]
    element_indicators = completion_indicators[4:]
    
    logger.info(f"URL completion indicators: {len(url_indicators)}")
    for indicator in url_indicators:
        logger.debug(f"  URL: {indicator}")
    
    logger.info(f"Element completion indicators: {len(element_indicators)}")
    for indicator in element_indicators:
        logger.debug(f"  Element: {indicator}")
    
    # Validate element selectors
    valid_element_selectors = 0
    for selector in element_indicators:
        if selector.startswith('//') and ('[' in selector or '@' in selector):
            valid_element_selectors += 1
    
    element_success_rate = (valid_element_selectors / len(element_indicators)) * 100
    
    logger.info(f"Completion indicator validation:")
    logger.info(f"  URL indicators: {len(url_indicators)} (all valid)")
    logger.info(f"  Element indicators: {len(element_indicators)}")
    logger.info(f"  Valid element selectors: {valid_element_selectors}")
    logger.info(f"  Element success rate: {element_success_rate:.1f}%")
    
    return element_success_rate >= 90.0

def run_enhanced_submit_tests():
    """Run all enhanced submit detection tests"""
    logger.info("=" * 60)
    logger.info("ENHANCED SUBMIT BUTTON DETECTION TESTS")
    logger.info("=" * 60)
    
    tests = [
        ("Submit Button Selectors", test_submit_button_selectors),
        ("SMS Input Selectors", test_sms_input_selectors),
        ("Verification Completion Indicators", test_verification_completion_indicators)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\nRunning test: {test_name}")
        logger.info("-" * 40)
        
        try:
            success = test_func()
            status = "PASSED" if success else "FAILED"
            results.append((test_name, status))
            logger.info(f"Test {test_name}: {status}")
        except Exception as e:
            results.append((test_name, "ERROR"))
            logger.error(f"Test {test_name}: ERROR - {str(e)}")
    
    # Generate summary
    logger.info("\n" + "=" * 60)
    logger.info("TEST SUMMARY")
    logger.info("=" * 60)
    
    passed = sum(1 for _, status in results if status == "PASSED")
    failed = sum(1 for _, status in results if status == "FAILED")
    errors = sum(1 for _, status in results if status == "ERROR")
    total = len(results)
    
    logger.info(f"Total Tests: {total}")
    logger.info(f"Passed: {passed}")
    logger.info(f"Failed: {failed}")
    logger.info(f"Errors: {errors}")
    logger.info(f"Success Rate: {(passed/total*100):.1f}%")
    
    logger.info("\nDetailed Results:")
    for test_name, status in results:
        symbol = "✓" if status == "PASSED" else "✗"
        logger.info(f"{symbol} {test_name}: {status}")
    
    # Save results
    try:
        os.makedirs('grps/PyFiles/test_reports', exist_ok=True)
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'test_type': 'enhanced_submit_detection',
            'summary': {
                'total': total,
                'passed': passed,
                'failed': failed,
                'errors': errors,
                'success_rate': (passed/total*100) if total > 0 else 0
            },
            'results': [{'test': name, 'status': status} for name, status in results]
        }
        
        import json
        with open('grps/PyFiles/test_reports/enhanced_submit_test_report.json', 'w') as f:
            json.dump(report, f, indent=2)
        
        logger.info(f"\nTest report saved to: grps/PyFiles/test_reports/enhanced_submit_test_report.json")
        
    except Exception as e:
        logger.warning(f"Could not save test report: {str(e)}")
    
    logger.info("=" * 60)
    
    return passed == total

if __name__ == "__main__":
    success = run_enhanced_submit_tests()
    sys.exit(0 if success else 1)
