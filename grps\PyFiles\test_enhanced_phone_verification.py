#!/usr/bin/env python3
"""
Test script for Enhanced Phone Verification System
Tests the improved 5sim integration with retry logic, status tracking, and error handling.
"""

import sys
import os
import json
import logging
from datetime import datetime
from unittest.mock import Mock, patch

# Add the parent directory to the path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from groups import Groups
from fivesim_integration import FiveSimManager, FiveSimClient

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class TestEnhancedPhoneVerification:
    """Test class for enhanced phone verification functionality"""
    
    def __init__(self):
        self.test_email = "<EMAIL>"
        self.test_results = []
        
    def setup_test_environment(self):
        """Setup test environment with mock data"""
        logger.info("Setting up test environment...")
        
        # Create mock browser object
        mock_browser = Mock()
        mock_browser.email = self.test_email
        mock_browser.find_xpath = Mock()
        
        # Create Groups instance for testing
        self.groups = Groups()
        self.groups.browser = mock_browser
        self.groups.logger = logger
        
        return True
    
    def test_sms_verification_status_tracking(self):
        """Test SMS verification status tracking functionality"""
        logger.info("Testing SMS verification status tracking...")
        
        try:
            # Test checking SMS verification status
            should_verify = self.groups._should_perform_sms_verification()
            logger.info(f"Should perform SMS verification: {should_verify}")
            
            # Test updating SMS verification status
            self.groups._update_sms_verification_status(
                email=self.test_email,
                status=True,
                phone_number="+1234567890",
                verification_method="5sim_test"
            )
            
            # Verify status was updated
            should_verify_after = self.groups._should_perform_sms_verification()
            logger.info(f"Should perform SMS verification after update: {should_verify_after}")
            
            self.test_results.append({
                'test': 'sms_verification_status_tracking',
                'status': 'PASSED',
                'details': f'Status tracking working correctly'
            })
            
            return True
            
        except Exception as e:
            logger.error(f"SMS verification status tracking test failed: {str(e)}")
            self.test_results.append({
                'test': 'sms_verification_status_tracking',
                'status': 'FAILED',
                'error': str(e)
            })
            return False
    
    def test_fivesim_integration(self):
        """Test 5sim integration functionality"""
        logger.info("Testing 5sim integration...")
        
        try:
            # Create FiveSimManager instance
            fivesim_manager = FiveSimManager(logger)
            
            # Test availability check
            is_available = fivesim_manager.is_available()
            logger.info(f"5sim integration available: {is_available}")
            
            if is_available:
                # Test configuration loading
                config = fivesim_manager.config
                logger.info(f"5sim config loaded: {bool(config)}")
                
                # Test client initialization
                client = fivesim_manager.client
                logger.info(f"5sim client initialized: {bool(client)}")
                
                self.test_results.append({
                    'test': 'fivesim_integration',
                    'status': 'PASSED',
                    'details': f'5sim integration working, available: {is_available}'
                })
            else:
                self.test_results.append({
                    'test': 'fivesim_integration',
                    'status': 'SKIPPED',
                    'details': '5sim integration not configured or available'
                })
            
            return True
            
        except Exception as e:
            logger.error(f"5sim integration test failed: {str(e)}")
            self.test_results.append({
                'test': 'fivesim_integration',
                'status': 'FAILED',
                'error': str(e)
            })
            return False
    
    def test_code_extraction_validation(self):
        """Test SMS code extraction and validation"""
        logger.info("Testing SMS code extraction and validation...")
        
        try:
            # Create FiveSimClient for testing
            fivesim_client = FiveSimClient("test_api_key", logger)
            
            # Test various SMS message formats
            test_messages = [
                ("Your Google verification code is 123456", "123456"),
                ("G-789012 is your verification code", "789012"),
                ("Enter this code: 456789", "456789"),
                ("Verification code 321654", "321654"),
                ("Your code is: 987654", "987654"),
                ("Invalid message with no code", None),
                ("Code with year 2024 should be ignored", None),
                ("Multiple codes 123456 and 789012", "123456")  # Should get first valid one
            ]
            
            passed_tests = 0
            for message, expected_code in test_messages:
                extracted_code = fivesim_client._extract_verification_code(message)
                if extracted_code == expected_code:
                    passed_tests += 1
                    logger.info(f"✓ '{message}' -> '{extracted_code}' (expected: '{expected_code}')")
                else:
                    logger.warning(f"✗ '{message}' -> '{extracted_code}' (expected: '{expected_code}')")
            
            success_rate = passed_tests / len(test_messages)
            logger.info(f"Code extraction test success rate: {success_rate:.2%}")
            
            if success_rate >= 0.8:  # 80% success rate threshold
                self.test_results.append({
                    'test': 'code_extraction_validation',
                    'status': 'PASSED',
                    'details': f'Success rate: {success_rate:.2%} ({passed_tests}/{len(test_messages)})'
                })
                return True
            else:
                self.test_results.append({
                    'test': 'code_extraction_validation',
                    'status': 'FAILED',
                    'details': f'Low success rate: {success_rate:.2%} ({passed_tests}/{len(test_messages)})'
                })
                return False
            
        except Exception as e:
            logger.error(f"Code extraction validation test failed: {str(e)}")
            self.test_results.append({
                'test': 'code_extraction_validation',
                'status': 'FAILED',
                'error': str(e)
            })
            return False
    
    def test_enhanced_selectors(self):
        """Test enhanced SMS input field selectors"""
        logger.info("Testing enhanced SMS input field selectors...")
        
        try:
            # Mock browser with find_xpath method
            mock_browser = Mock()
            self.groups.browser = mock_browser
            
            # Test that selectors are comprehensive
            # This is a basic test to ensure the selector list is properly formatted
            test_method = self.groups._wait_and_enter_sms_code_with_5sim_enhanced
            
            # Check if method exists and is callable
            if callable(test_method):
                logger.info("Enhanced SMS code waiting method is available")
                self.test_results.append({
                    'test': 'enhanced_selectors',
                    'status': 'PASSED',
                    'details': 'Enhanced SMS input selectors method available'
                })
                return True
            else:
                self.test_results.append({
                    'test': 'enhanced_selectors',
                    'status': 'FAILED',
                    'details': 'Enhanced SMS input selectors method not available'
                })
                return False
            
        except Exception as e:
            logger.error(f"Enhanced selectors test failed: {str(e)}")
            self.test_results.append({
                'test': 'enhanced_selectors',
                'status': 'FAILED',
                'error': str(e)
            })
            return False
    
    def run_all_tests(self):
        """Run all tests and generate report"""
        logger.info("Starting Enhanced Phone Verification Test Suite...")
        logger.info("=" * 60)
        
        # Setup test environment
        if not self.setup_test_environment():
            logger.error("Failed to setup test environment")
            return False
        
        # Run individual tests
        tests = [
            self.test_sms_verification_status_tracking,
            self.test_fivesim_integration,
            self.test_code_extraction_validation,
            self.test_enhanced_selectors
        ]
        
        for test in tests:
            try:
                test()
            except Exception as e:
                logger.error(f"Test {test.__name__} failed with exception: {str(e)}")
        
        # Generate test report
        self.generate_test_report()
        
        return True
    
    def generate_test_report(self):
        """Generate and display test report"""
        logger.info("=" * 60)
        logger.info("ENHANCED PHONE VERIFICATION TEST REPORT")
        logger.info("=" * 60)
        
        passed = sum(1 for result in self.test_results if result['status'] == 'PASSED')
        failed = sum(1 for result in self.test_results if result['status'] == 'FAILED')
        skipped = sum(1 for result in self.test_results if result['status'] == 'SKIPPED')
        total = len(self.test_results)
        
        logger.info(f"Total Tests: {total}")
        logger.info(f"Passed: {passed}")
        logger.info(f"Failed: {failed}")
        logger.info(f"Skipped: {skipped}")
        logger.info(f"Success Rate: {(passed/total*100):.1f}%" if total > 0 else "N/A")
        logger.info("-" * 60)
        
        for result in self.test_results:
            status_symbol = "✓" if result['status'] == 'PASSED' else "✗" if result['status'] == 'FAILED' else "⚠"
            logger.info(f"{status_symbol} {result['test']}: {result['status']}")
            if 'details' in result:
                logger.info(f"   Details: {result['details']}")
            if 'error' in result:
                logger.info(f"   Error: {result['error']}")
        
        logger.info("=" * 60)
        
        # Save report to file
        report_data = {
            'timestamp': datetime.now().isoformat(),
            'summary': {
                'total': total,
                'passed': passed,
                'failed': failed,
                'skipped': skipped,
                'success_rate': (passed/total*100) if total > 0 else 0
            },
            'test_results': self.test_results
        }
        
        try:
            with open('grps/PyFiles/test_reports/phone_verification_test_report.json', 'w') as f:
                json.dump(report_data, f, indent=2)
            logger.info("Test report saved to: grps/PyFiles/test_reports/phone_verification_test_report.json")
        except Exception as e:
            logger.warning(f"Could not save test report: {str(e)}")

if __name__ == "__main__":
    # Create test reports directory if it doesn't exist
    os.makedirs('grps/PyFiles/test_reports', exist_ok=True)
    
    # Run tests
    test_suite = TestEnhancedPhoneVerification()
    test_suite.run_all_tests()
