#!/usr/bin/env python3
"""
Simplified test script for Enhanced Phone Verification Components
Tests individual components without triggering the full Groups application.
"""

import sys
import os
import json
import logging
from datetime import datetime

# Add the parent directory to the path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_fivesim_integration():
    """Test 5sim integration components"""
    logger.info("Testing 5sim integration components...")
    
    try:
        from fivesim_integration import FiveSimManager, FiveSimClient
        
        # Test FiveSimManager initialization
        fivesim_manager = FiveSimManager(logger)
        logger.info("✓ FiveSimManager initialized successfully")
        
        # Test availability check
        is_available = fivesim_manager.is_available()
        logger.info(f"✓ 5sim availability check: {is_available}")
        
        # Test FiveSimClient initialization
        fivesim_client = FiveSimClient("test_api_key", logger)
        logger.info("✓ FiveSimClient initialized successfully")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ 5sim integration test failed: {str(e)}")
        return False

def test_code_extraction():
    """Test SMS code extraction functionality"""
    logger.info("Testing SMS code extraction...")
    
    try:
        from fivesim_integration import FiveSimClient
        
        # Create client for testing
        client = FiveSimClient("test_api_key", logger)
        
        # Test various SMS message formats
        test_cases = [
            ("Your Google verification code is 123456", "123456"),
            ("G-789012 is your verification code", "789012"),
            ("Enter this code: 456789", "456789"),
            ("Verification code 321654", "321654"),
            ("Your code is: 987654", "987654"),
            ("Invalid message with no code", None),
            ("Code with year 2024 should be ignored", None)
        ]
        
        passed = 0
        total = len(test_cases)
        
        for message, expected in test_cases:
            result = client._extract_verification_code(message)
            if result == expected:
                logger.info(f"✓ '{message[:30]}...' -> '{result}'")
                passed += 1
            else:
                logger.warning(f"✗ '{message[:30]}...' -> '{result}' (expected: '{expected}')")
        
        success_rate = passed / total
        logger.info(f"Code extraction success rate: {success_rate:.2%} ({passed}/{total})")
        
        return success_rate >= 0.8
        
    except Exception as e:
        logger.error(f"✗ Code extraction test failed: {str(e)}")
        return False

def test_config_loading():
    """Test configuration loading"""
    logger.info("Testing configuration loading...")
    
    try:
        config_file = "grps/PyFiles/json/fivesim_config.json"
        
        if os.path.exists(config_file):
            with open(config_file, 'r') as f:
                config = json.load(f)
            
            required_keys = ['enabled', 'api_key', 'default_country', 'product']
            missing_keys = [key for key in required_keys if key not in config]
            
            if not missing_keys:
                logger.info("✓ 5sim configuration loaded successfully")
                logger.info(f"✓ Configuration enabled: {config.get('enabled', False)}")
                return True
            else:
                logger.warning(f"✗ Missing configuration keys: {missing_keys}")
                return False
        else:
            logger.warning("✗ 5sim configuration file not found")
            return False
            
    except Exception as e:
        logger.error(f"✗ Configuration loading test failed: {str(e)}")
        return False

def test_gmail_accounts_map():
    """Test Gmail accounts map functionality"""
    logger.info("Testing Gmail accounts map...")
    
    try:
        gmail_map_file = "grps/PyFiles/json/GmailAccountsMap.json"
        
        if os.path.exists(gmail_map_file):
            with open(gmail_map_file, 'r') as f:
                data = json.load(f)
            
            if isinstance(data, list) and len(data) > 0:
                logger.info(f"✓ Gmail accounts map loaded: {len(data)} accounts")
                
                # Check for required fields
                sample_account = data[0]
                required_fields = ['email', 'password', 'status']
                missing_fields = [field for field in required_fields if field not in sample_account]
                
                if not missing_fields:
                    logger.info("✓ Gmail accounts map structure is valid")
                    
                    # Check for enhanced fields
                    enhanced_fields = ['language_changed', 'sms_verification_completed', 'chrome_sync_enabled']
                    present_enhanced = [field for field in enhanced_fields if field in sample_account]
                    logger.info(f"✓ Enhanced fields present: {present_enhanced}")
                    
                    return True
                else:
                    logger.warning(f"✗ Missing required fields: {missing_fields}")
                    return False
            else:
                logger.warning("✗ Gmail accounts map is empty or invalid")
                return False
        else:
            logger.warning("✗ Gmail accounts map file not found")
            return False
            
    except Exception as e:
        logger.error(f"✗ Gmail accounts map test failed: {str(e)}")
        return False

def test_enhanced_selectors():
    """Test enhanced selector patterns"""
    logger.info("Testing enhanced selector patterns...")
    
    try:
        # Test selector patterns (basic validation)
        sms_selectors = [
            '//input[@id="idvAnyPhonePin"]',
            '//input[@name="pin"]',
            '//input[@type="tel" and contains(@aria-label, "Enter code")]',
            '//input[@type="tel" and @pattern="[0-9 ]*"]',
            '//input[contains(@aria-label, "verification code")]',
            '//input[contains(@placeholder, "Enter code")]',
            '//input[@type="tel" and @inputmode="numeric"]',
            '//input[contains(@class, "verification")]'
        ]
        
        # Basic validation - check if selectors are properly formatted XPath
        valid_selectors = 0
        for selector in sms_selectors:
            if selector.startswith('//') and '[' in selector and ']' in selector:
                valid_selectors += 1
        
        success_rate = valid_selectors / len(sms_selectors)
        logger.info(f"✓ Enhanced selectors validation: {success_rate:.2%} ({valid_selectors}/{len(sms_selectors)})")
        
        return success_rate >= 0.9
        
    except Exception as e:
        logger.error(f"✗ Enhanced selectors test failed: {str(e)}")
        return False

def run_component_tests():
    """Run all component tests"""
    logger.info("=" * 60)
    logger.info("ENHANCED PHONE VERIFICATION COMPONENT TESTS")
    logger.info("=" * 60)
    
    tests = [
        ("5sim Integration", test_fivesim_integration),
        ("Code Extraction", test_code_extraction),
        ("Configuration Loading", test_config_loading),
        ("Gmail Accounts Map", test_gmail_accounts_map),
        ("Enhanced Selectors", test_enhanced_selectors)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\nRunning test: {test_name}")
        logger.info("-" * 40)
        
        try:
            success = test_func()
            status = "PASSED" if success else "FAILED"
            results.append((test_name, status))
            logger.info(f"Test {test_name}: {status}")
        except Exception as e:
            results.append((test_name, "ERROR"))
            logger.error(f"Test {test_name}: ERROR - {str(e)}")
    
    # Generate summary
    logger.info("\n" + "=" * 60)
    logger.info("TEST SUMMARY")
    logger.info("=" * 60)
    
    passed = sum(1 for _, status in results if status == "PASSED")
    failed = sum(1 for _, status in results if status == "FAILED")
    errors = sum(1 for _, status in results if status == "ERROR")
    total = len(results)
    
    logger.info(f"Total Tests: {total}")
    logger.info(f"Passed: {passed}")
    logger.info(f"Failed: {failed}")
    logger.info(f"Errors: {errors}")
    logger.info(f"Success Rate: {(passed/total*100):.1f}%")
    
    logger.info("\nDetailed Results:")
    for test_name, status in results:
        symbol = "✓" if status == "PASSED" else "✗"
        logger.info(f"{symbol} {test_name}: {status}")
    
    # Save results
    try:
        os.makedirs('grps/PyFiles/test_reports', exist_ok=True)
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'test_type': 'component_tests',
            'summary': {
                'total': total,
                'passed': passed,
                'failed': failed,
                'errors': errors,
                'success_rate': (passed/total*100) if total > 0 else 0
            },
            'results': [{'test': name, 'status': status} for name, status in results]
        }
        
        with open('grps/PyFiles/test_reports/component_test_report.json', 'w') as f:
            json.dump(report, f, indent=2)
        
        logger.info(f"\nTest report saved to: grps/PyFiles/test_reports/component_test_report.json")
        
    except Exception as e:
        logger.warning(f"Could not save test report: {str(e)}")
    
    logger.info("=" * 60)
    
    return passed == total

if __name__ == "__main__":
    success = run_component_tests()
    sys.exit(0 if success else 1)
